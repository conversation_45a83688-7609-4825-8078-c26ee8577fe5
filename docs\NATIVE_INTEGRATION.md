# React Native Stallion - Native Integration Guide

This guide covers the native platform integration for React Native Stallion OTA updates.

## Prerequisites

- React Native 0.60+ (for autolinking support)
- Android API level 21+ (Android 5.0+)
- iOS 11.0+

## Automatic Integration (Recommended)

React Native Stallion uses autolinking, so most integration is automatic. However, you may need to perform some manual steps.

## Android Integration

### 1. Permissions

The following permissions are automatically added by the Stallion package, but verify they exist in `android/app/src/main/AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### 2. ProGuard Configuration (Release Builds)

If you're using ProGuard, add these rules to `android/app/proguard-rules.pro`:

```proguard
# React Native Stallion
-keep class com.stallion.** { *; }
-keep class io.stallion.** { *; }
-dontwarn com.stallion.**
-dontwarn io.stallion.**
```

### 3. Build Configuration

Ensure your `android/app/build.gradle` has the correct configuration:

```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
        // ... other config
    }
}
```

## iOS Integration

### 1. Info.plist Configuration

Add the following to `ios/YourApp/Info.plist` if not already present:

```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <false/>
    <key>NSAllowsLocalNetworking</key>
    <true/>
</dict>
```

### 2. CocoaPods Installation

Run the following command to install iOS dependencies:

```bash
cd ios && pod install
```

### 3. Build Settings

Ensure your iOS deployment target is set to 11.0 or higher in Xcode.

## Manual Linking (React Native < 0.60)

If you're using React Native < 0.60, you'll need to manually link the package:

### Android Manual Linking

1. Add to `android/settings.gradle`:
```gradle
include ':react-native-stallion'
project(':react-native-stallion').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-stallion/android')
```

2. Add to `android/app/build.gradle`:
```gradle
dependencies {
    implementation project(':react-native-stallion')
}
```

3. Add to `MainApplication.java`:
```java
import com.stallion.StallionPackage;

@Override
protected List<ReactPackage> getPackages() {
    return Arrays.<ReactPackage>asList(
        new MainReactPackage(),
        new StallionPackage()
    );
}
```

### iOS Manual Linking

1. Add to `ios/Podfile`:
```ruby
pod 'react-native-stallion', :path => '../node_modules/react-native-stallion'
```

2. Run `pod install`

## Troubleshooting

### Common Issues

1. **Build Errors**: Clean and rebuild your project
   ```bash
   # Android
   cd android && ./gradlew clean
   
   # iOS
   cd ios && xcodebuild clean
   ```

2. **Metro Cache Issues**: Clear Metro cache
   ```bash
   npx react-native start --reset-cache
   ```

3. **Autolinking Issues**: Manually run autolinking
   ```bash
   npx react-native unlink react-native-stallion
   npx react-native link react-native-stallion
   ```

### Verification

To verify the integration is working:

1. Check that the Stallion package is properly linked
2. Run the app and check for any native module errors
3. Test OTA update functionality in development

## Next Steps

After completing native integration:

1. Configure your Stallion credentials in `stallion.config.js`
2. Initialize the Stallion service in your app
3. Test OTA updates in development environment
4. Deploy to staging/production

For more information, see the main documentation in `docs/README.md`.
