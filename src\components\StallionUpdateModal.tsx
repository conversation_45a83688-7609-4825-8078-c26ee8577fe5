/**
 * Enhanced Stallion Update Modal Component
 *
 * Advanced React Native component for displaying OTA update notifications and progress
 * Features: Better UX, animations, user preferences, detailed progress tracking
 *
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  Alert,
  BackHandler,
  Animated,
  ScrollView,
  Switch,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import stallionOTAService, {
  UpdateStatus,
  UpdateInfo,
  UpdateProgress,
  UpdatePreferences,
  UpdateHistoryEntry
} from '../services/stallionOTAService';
import logger from '../services/productionLogger';
import StallionSettingsPanel from './StallionSettingsPanel';

const { width, height } = Dimensions.get('window');

interface StallionUpdateModalProps {
  visible: boolean;
  onClose: () => void;
  showSettings?: boolean;
}

const StallionUpdateModal: React.FC<StallionUpdateModalProps> = ({
  visible,
  onClose,
  showSettings = false
}) => {
  // State management
  const [updateStatus, setUpdateStatus] = useState<UpdateStatus>(UpdateStatus.UP_TO_DATE);
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null);
  const [progress, setProgress] = useState<UpdateProgress | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [userPreferences, setUserPreferences] = useState<UpdatePreferences>({
    autoDownload: false,
    wifiOnly: false,
    minBatteryLevel: 20,
    allowInBackground: true,
    notifyOnAvailable: true,
  });
  const [updateHistory, setUpdateHistory] = useState<UpdateHistoryEntry[]>([]);
  const [deviceStatus, setDeviceStatus] = useState({ battery: 100, network: { isConnected: false, type: 'unknown', isWiFi: false } });

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  // Initialize component data
  useEffect(() => {
    if (visible) {
      loadComponentData();
      startAnimations();
    }
  }, [visible]);

  // Load initial data
  const loadComponentData = async () => {
    try {
      const prefs = stallionOTAService.getUserPreferences();
      const history = stallionOTAService.getUpdateHistory();
      const status = stallionOTAService.getDeviceStatus();

      setUserPreferences(prefs);
      setUpdateHistory(history);
      setDeviceStatus(status);
    } catch (error) {
      logger.warn('[UPDATE_MODAL] Failed to load component data', { error }, 'update');
    }
  };

  // Start entrance animations
  const startAnimations = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Update event listener
  useEffect(() => {
    const handleUpdateEvent = (status: UpdateStatus, info?: UpdateInfo, progressInfo?: UpdateProgress) => {
      setUpdateStatus(status);
      setUpdateInfo(info || null);
      setProgress(progressInfo || null);

      // Update local state flags
      setIsDownloading(status === UpdateStatus.DOWNLOADING);
      setIsInstalling(status === UpdateStatus.INSTALLING);

      // Handle automatic restart for mandatory updates
      if (status === UpdateStatus.RESTART_REQUIRED && info?.isMandatory) {
        setTimeout(() => {
          stallionOTAService.restartApp();
        }, 2000);
      }

      // Refresh device status during updates
      if (status === UpdateStatus.DOWNLOADING || status === UpdateStatus.INSTALLING) {
        const deviceStatus = stallionOTAService.getDeviceStatus();
        setDeviceStatus(deviceStatus);
      }
    };

    stallionOTAService.addEventListener(handleUpdateEvent);

    return () => {
      stallionOTAService.removeEventListener(handleUpdateEvent);
    };
  }, []);

  useEffect(() => {
    // Prevent back button during mandatory updates
    if (updateInfo?.isMandatory && visible) {
      const backHandler = BackHandler.addEventListener('hardwareBackPress', () => true);
      return () => backHandler.remove();
    }
  }, [updateInfo?.isMandatory, visible]);

  // Enhanced action handlers
  const handleDownloadUpdate = async () => {
    try {
      logger.info('[UPDATE_MODAL] User initiated update download', null, 'update');

      // Check device conditions before starting
      const deviceStatus = stallionOTAService.getDeviceStatus();
      if (deviceStatus.battery < userPreferences.minBatteryLevel) {
        Alert.alert(
          'Low Battery',
          `Battery level is ${Math.round(deviceStatus.battery)}%. Please charge to at least ${userPreferences.minBatteryLevel}% before updating.`,
          [{ text: 'OK' }]
        );
        return;
      }

      if (userPreferences.wifiOnly && !deviceStatus.network.isWiFi) {
        Alert.alert(
          'WiFi Required',
          'Your settings require WiFi for updates. Please connect to WiFi and try again.',
          [{ text: 'OK' }]
        );
        return;
      }

      await stallionOTAService.downloadAndInstall();
    } catch (error) {
      logger.error('[UPDATE_MODAL] Failed to download update', error, 'update');
      Alert.alert('Update Error', 'Failed to download update. Please try again later.');
    }
  };

  const handleSkipUpdate = () => {
    if (updateInfo?.isMandatory) {
      Alert.alert(
        'Mandatory Update',
        'This update is required to continue using the app.',
        [{ text: 'OK' }]
      );
      return;
    }

    logger.info('[UPDATE_MODAL] User skipped optional update', null, 'update');
    onClose();
  };

  const handleRestartApp = () => {
    logger.info('[UPDATE_MODAL] User initiated app restart', null, 'update');
    stallionOTAService.restartApp();
  };

  const handleRollback = () => {
    Alert.alert(
      'Rollback Update',
      'Are you sure you want to rollback to the previous version? This will restart the app.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Rollback',
          style: 'destructive',
          onPress: async () => {
            try {
              await stallionOTAService.rollbackUpdate();
            } catch (error) {
              Alert.alert('Rollback Failed', 'Unable to rollback the update. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handlePreferenceChange = async (key: keyof UpdatePreferences, value: any) => {
    try {
      const newPreferences = { ...userPreferences, [key]: value };
      setUserPreferences(newPreferences);
      await stallionOTAService.updateUserPreferences({ [key]: value });
      logger.info('[UPDATE_MODAL] User preference updated', { key, value }, 'update');
    } catch (error) {
      logger.error('[UPDATE_MODAL] Failed to update preference', { key, value, error }, 'update');
    }
  };

  // Enhanced status and progress methods
  const getStatusMessage = (): string => {
    switch (updateStatus) {
      case UpdateStatus.CHECKING:
        return 'Checking for updates...';
      case UpdateStatus.DOWNLOADING:
        const percentage = progress?.percentage || 0;
        const speed = progress?.speed ? formatBytes(progress.speed) + '/s' : '';
        const eta = progress?.estimatedTimeRemaining ? formatTime(progress.estimatedTimeRemaining) : '';
        return `Downloading update... ${percentage}%${speed ? ` • ${speed}` : ''}${eta ? ` • ${eta} remaining` : ''}`;
      case UpdateStatus.INSTALLING:
        return 'Installing update...';
      case UpdateStatus.RESTART_REQUIRED:
        return 'Update installed successfully!';
      case UpdateStatus.ERROR:
        return 'Update failed. Please try again.';
      default:
        return updateInfo?.isMandatory
          ? 'A mandatory update is available'
          : 'An update is available';
    }
  };

  const getProgressPercentage = (): number => {
    if (updateStatus === UpdateStatus.DOWNLOADING && progress) {
      return progress.percentage;
    }
    if (updateStatus === UpdateStatus.INSTALLING) {
      return 100;
    }
    return 0;
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getNetworkStatusIcon = (): string => {
    if (!deviceStatus.network.isConnected) return '📶❌';
    if (deviceStatus.network.isWiFi) return '📶📡';
    return '📶📱';
  };

  const getBatteryStatusIcon = (): string => {
    const battery = Math.round(deviceStatus.battery);
    if (battery > 75) return '🔋';
    if (battery > 50) return '🔋';
    if (battery > 25) return '🪫';
    return '🪫❗';
  };

  const isUpdateInProgress = isDownloading || isInstalling;
  const showProgressBar = updateStatus === UpdateStatus.DOWNLOADING || updateStatus === UpdateStatus.INSTALLING;

  if (!visible || updateStatus === UpdateStatus.UP_TO_DATE) {
    return null;
  }

  // Show settings panel if requested
  if (showSettings || showAdvanced) {
    return (
      <Modal
        visible={visible}
        transparent
        animationType="slide"
        onRequestClose={() => {
          setShowAdvanced(false);
          if (showSettings) onClose();
        }}
      >
        <View style={styles.overlay}>
          <View style={[styles.modal, styles.settingsModal]}>
            <StallionSettingsPanel
              preferences={userPreferences}
              onPreferenceChange={handlePreferenceChange}
              updateHistory={updateHistory}
              deviceStatus={deviceStatus}
              onClose={() => {
                setShowAdvanced(false);
                if (showSettings) onClose();
              }}
            />
          </View>
        </View>
      </Modal>
    );
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={updateInfo?.isMandatory ? undefined : onClose}
    >
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.modal,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <LinearGradient
            colors={['#667eea', '#764ba2']}
            style={styles.header}
          >
            <View style={styles.headerContent}>
              <Text style={styles.title}>
                {updateInfo?.isMandatory ? 'Mandatory Update' : 'Update Available'}
              </Text>
              {!updateInfo?.isMandatory && (
                <TouchableOpacity
                  onPress={() => setShowAdvanced(true)}
                  style={styles.settingsButton}
                >
                  <Text style={styles.settingsButtonText}>⚙️</Text>
                </TouchableOpacity>
              )}
            </View>
          </LinearGradient>

          <ScrollView style={styles.content}>
            <Text style={styles.statusText}>
              {getStatusMessage()}
            </Text>

            {/* Device Status Bar */}
            <View style={styles.deviceStatus}>
              <View style={styles.deviceStatusItem}>
                <Text style={styles.deviceStatusIcon}>{getBatteryStatusIcon()}</Text>
                <Text style={styles.deviceStatusText}>{Math.round(deviceStatus.battery)}%</Text>
              </View>
              <View style={styles.deviceStatusItem}>
                <Text style={styles.deviceStatusIcon}>{getNetworkStatusIcon()}</Text>
                <Text style={styles.deviceStatusText}>
                  {deviceStatus.network.isWiFi ? 'WiFi' : deviceStatus.network.type}
                </Text>
              </View>
            </View>

            {updateInfo && (
              <View style={styles.updateInfo}>
                <Text style={styles.versionText}>
                  Version {updateInfo.version} (Build {updateInfo.buildNumber})
                </Text>

                {updateInfo.description && (
                  <Text style={styles.descriptionText}>
                    {updateInfo.description}
                  </Text>
                )}

                {updateInfo.changelog && updateInfo.changelog.length > 0 && (
                  <View style={styles.changelogContainer}>
                    <Text style={styles.changelogTitle}>What's New:</Text>
                    {updateInfo.changelog.slice(0, 3).map((item, index) => (
                      <Text key={index} style={styles.changelogItem}>• {item}</Text>
                    ))}
                  </View>
                )}

                <View style={styles.updateMetadata}>
                  {updateInfo.packageSize && (
                    <Text style={styles.metadataText}>
                      Size: {formatBytes(updateInfo.packageSize)}
                    </Text>
                  )}
                  {updateInfo.releaseDate && (
                    <Text style={styles.metadataText}>
                      Released: {new Date(updateInfo.releaseDate).toLocaleDateString()}
                    </Text>
                  )}
                </View>
              </View>
            )}

            {showProgressBar && (
              <View style={styles.progressContainer}>
                <View style={styles.progressHeader}>
                  <Text style={styles.progressLabel}>
                    {updateStatus === UpdateStatus.DOWNLOADING ? 'Downloading' : 'Installing'}
                  </Text>
                  <Text style={styles.progressPercentage}>
                    {getProgressPercentage()}%
                  </Text>
                </View>
                <View style={styles.progressBar}>
                  <Animated.View
                    style={[
                      styles.progressFill,
                      { width: `${getProgressPercentage()}%` }
                    ]}
                  />
                </View>
                {progress && updateStatus === UpdateStatus.DOWNLOADING && (
                  <View style={styles.progressDetails}>
                    <Text style={styles.progressDetailText}>
                      {formatBytes(progress.bytesReceived)} / {formatBytes(progress.totalBytes)}
                    </Text>
                    {progress.speed && (
                      <Text style={styles.progressDetailText}>
                        {formatBytes(progress.speed)}/s
                      </Text>
                    )}
                  </View>
                )}
              </View>
            )}

            {updateStatus === UpdateStatus.ERROR && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>
                  Update failed. Please check your internet connection and try again.
                </Text>
                <TouchableOpacity
                  onPress={handleRollback}
                  style={styles.rollbackButton}
                >
                  <Text style={styles.rollbackButtonText}>Rollback to Previous Version</Text>
                </TouchableOpacity>
              </View>
            )}
          </ScrollView>

          <View style={styles.actions}>
            {updateStatus === UpdateStatus.UPDATE_AVAILABLE && !isUpdateInProgress && (
              <>
                <TouchableOpacity
                  style={[styles.button, styles.primaryButton]}
                  onPress={handleDownloadUpdate}
                >
                  <Text style={styles.primaryButtonText}>
                    {updateInfo?.isMandatory ? 'Update Now' : 'Download Update'}
                  </Text>
                </TouchableOpacity>

                {!updateInfo?.isMandatory && (
                  <TouchableOpacity
                    style={[styles.button, styles.secondaryButton]}
                    onPress={handleSkipUpdate}
                  >
                    <Text style={styles.secondaryButtonText}>Later</Text>
                  </TouchableOpacity>
                )}
              </>
            )}

            {updateStatus === UpdateStatus.RESTART_REQUIRED && (
              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={handleRestartApp}
              >
                <Text style={styles.primaryButtonText}>
                  {updateInfo?.isMandatory ? 'Restarting...' : 'Restart App'}
                </Text>
              </TouchableOpacity>
            )}

            {updateStatus === UpdateStatus.ERROR && (
              <>
                <TouchableOpacity
                  style={[styles.button, styles.primaryButton]}
                  onPress={handleDownloadUpdate}
                >
                  <Text style={styles.primaryButtonText}>Try Again</Text>
                </TouchableOpacity>

                {!updateInfo?.isMandatory && (
                  <TouchableOpacity
                    style={[styles.button, styles.secondaryButton]}
                    onPress={handleSkipUpdate}
                  >
                    <Text style={styles.secondaryButtonText}>Cancel</Text>
                  </TouchableOpacity>
                )}
              </>
            )}

            {isUpdateInProgress && (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color="#667eea" />
                <Text style={styles.loadingText}>
                  {updateStatus === UpdateStatus.DOWNLOADING ? 'Downloading...' : 'Installing...'}
                </Text>
              </View>
            )}
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    width: width * 0.9,
    maxWidth: 400,
    maxHeight: height * 0.8,
    backgroundColor: '#fff',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  settingsModal: {
    maxHeight: height * 0.9,
    width: width * 0.95,
  },
  header: {
    padding: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
  },
  settingsButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  settingsButtonText: {
    fontSize: 16,
    color: '#fff',
  },
  content: {
    maxHeight: height * 0.4,
    padding: 20,
  },
  statusText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  deviceStatus: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  deviceStatusItem: {
    alignItems: 'center',
  },
  deviceStatusIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  deviceStatusText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  updateInfo: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  versionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#495057',
    marginBottom: 8,
  },
  descriptionText: {
    fontSize: 14,
    color: '#6c757d',
    lineHeight: 20,
    marginBottom: 12,
  },
  changelogContainer: {
    marginBottom: 12,
  },
  changelogTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#495057',
    marginBottom: 8,
  },
  changelogItem: {
    fontSize: 13,
    color: '#6c757d',
    lineHeight: 18,
    marginBottom: 4,
  },
  updateMetadata: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  metadataText: {
    fontSize: 12,
    color: '#868e96',
    marginBottom: 4,
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#495057',
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: '600',
    color: '#667eea',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#e9ecef',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#667eea',
    borderRadius: 4,
  },
  progressDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  progressDetailText: {
    fontSize: 12,
    color: '#6c757d',
  },
  errorContainer: {
    backgroundColor: '#f8d7da',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#f5c6cb',
  },
  errorText: {
    fontSize: 14,
    color: '#721c24',
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 20,
  },
  rollbackButton: {
    backgroundColor: '#dc3545',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignSelf: 'center',
  },
  rollbackButtonText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '500',
  },
  actions: {
    padding: 20,
    paddingTop: 0,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginBottom: 12,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#667eea',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6c757d',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  loadingText: {
    fontSize: 14,
    color: '#6c757d',
    marginLeft: 8,
  },
});

export default StallionUpdateModal;
