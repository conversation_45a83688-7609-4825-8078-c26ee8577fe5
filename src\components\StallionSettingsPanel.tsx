/**
 * Stallion Settings Panel Component
 * 
 * Settings panel for configuring OTA update preferences
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import React from 'react';
import {
  View,
  Text,
  Switch,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { UpdatePreferences, UpdateHistoryEntry } from '../services/stallionOTAService';

interface StallionSettingsPanelProps {
  preferences: UpdatePreferences;
  onPreferenceChange: (key: keyof UpdatePreferences, value: any) => void;
  updateHistory: UpdateHistoryEntry[];
  deviceStatus: { battery: number; network: { isConnected: boolean; type: string; isWiFi: boolean } };
  onClose: () => void;
}

const StallionSettingsPanel: React.FC<StallionSettingsPanelProps> = ({
  preferences,
  onPreferenceChange,
  updateHistory,
  deviceStatus,
  onClose,
}) => {
  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleDateString() + ' ' + new Date(timestamp).toLocaleTimeString();
  };

  const getStatusIcon = (status: string): string => {
    switch (status) {
      case 'success': return '✅';
      case 'failed': return '❌';
      case 'rolled_back': return '↩️';
      default: return '❓';
    }
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Update Settings</Text>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Text style={styles.closeButtonText}>✕</Text>
        </TouchableOpacity>
      </View>

      {/* Device Status */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Device Status</Text>
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Battery:</Text>
          <Text style={styles.statusValue}>{Math.round(deviceStatus.battery)}%</Text>
        </View>
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Network:</Text>
          <Text style={styles.statusValue}>
            {deviceStatus.network.isConnected 
              ? `${deviceStatus.network.type}${deviceStatus.network.isWiFi ? ' (WiFi)' : ''}`
              : 'Disconnected'
            }
          </Text>
        </View>
      </View>

      {/* Update Preferences */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Update Preferences</Text>
        
        <View style={styles.preferenceRow}>
          <View style={styles.preferenceInfo}>
            <Text style={styles.preferenceLabel}>Auto Download</Text>
            <Text style={styles.preferenceDescription}>Automatically download available updates</Text>
          </View>
          <Switch
            value={preferences.autoDownload}
            onValueChange={(value) => onPreferenceChange('autoDownload', value)}
          />
        </View>

        <View style={styles.preferenceRow}>
          <View style={styles.preferenceInfo}>
            <Text style={styles.preferenceLabel}>WiFi Only</Text>
            <Text style={styles.preferenceDescription}>Only download updates on WiFi</Text>
          </View>
          <Switch
            value={preferences.wifiOnly}
            onValueChange={(value) => onPreferenceChange('wifiOnly', value)}
          />
        </View>

        <View style={styles.preferenceRow}>
          <View style={styles.preferenceInfo}>
            <Text style={styles.preferenceLabel}>Background Updates</Text>
            <Text style={styles.preferenceDescription}>Allow updates when app is in background</Text>
          </View>
          <Switch
            value={preferences.allowInBackground}
            onValueChange={(value) => onPreferenceChange('allowInBackground', value)}
          />
        </View>

        <View style={styles.preferenceRow}>
          <View style={styles.preferenceInfo}>
            <Text style={styles.preferenceLabel}>Update Notifications</Text>
            <Text style={styles.preferenceDescription}>Show notifications when updates are available</Text>
          </View>
          <Switch
            value={preferences.notifyOnAvailable}
            onValueChange={(value) => onPreferenceChange('notifyOnAvailable', value)}
          />
        </View>

        <View style={styles.preferenceRow}>
          <View style={styles.preferenceInfo}>
            <Text style={styles.preferenceLabel}>Minimum Battery Level</Text>
            <Text style={styles.preferenceDescription}>Required battery level for updates: {preferences.minBatteryLevel}%</Text>
          </View>
        </View>
      </View>

      {/* Update History */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Update History</Text>
        {updateHistory.length === 0 ? (
          <Text style={styles.emptyText}>No update history available</Text>
        ) : (
          updateHistory.slice(0, 10).map((entry, index) => (
            <View key={index} style={styles.historyItem}>
              <View style={styles.historyHeader}>
                <Text style={styles.historyVersion}>
                  {getStatusIcon(entry.status)} v{entry.version} ({entry.buildNumber})
                </Text>
                <Text style={styles.historyDate}>{formatDate(entry.timestamp)}</Text>
              </View>
              {entry.error && (
                <Text style={styles.historyError}>{entry.error}</Text>
              )}
            </View>
          ))
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 18,
    color: '#666',
  },
  section: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f8f9fa',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statusLabel: {
    fontSize: 14,
    color: '#666',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  preferenceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  preferenceInfo: {
    flex: 1,
    marginRight: 16,
  },
  preferenceLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  preferenceDescription: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
  },
  historyItem: {
    marginBottom: 12,
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  historyVersion: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  historyDate: {
    fontSize: 12,
    color: '#666',
  },
  historyError: {
    fontSize: 12,
    color: '#dc3545',
    marginTop: 4,
    fontStyle: 'italic',
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default StallionSettingsPanel;
