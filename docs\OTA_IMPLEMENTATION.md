# Vendy React Native Stallion OTA Implementation

Complete Over-The-Air (OTA) update system for the Vendy React Native application using React Native Stallion.

## Table of Contents

1. [Overview](#overview)
2. [Features](#features)
3. [Quick Start](#quick-start)
4. [Architecture](#architecture)
5. [Configuration](#configuration)
6. [Usage](#usage)
7. [Testing](#testing)
8. [Deployment](#deployment)
9. [Troubleshooting](#troubleshooting)
10. [API Reference](#api-reference)

## Overview

This implementation provides a comprehensive OTA update system that allows you to deploy JavaScript bundle updates to your React Native app without going through app store releases. Built on React Native Stallion, it offers enterprise-grade features including:

- **Automatic and manual update detection**
- **Progressive rollouts with percentage control**
- **Mandatory and optional updates**
- **Automatic rollback on failures**
- **User preference management**
- **Comprehensive error handling**
- **Real-time progress tracking**
- **Device condition monitoring**

## Features

### Core Features
- ✅ **Seamless Integration**: Drop-in solution with minimal setup
- ✅ **Smart Update Detection**: Automatic checks with configurable intervals
- ✅ **Progressive Rollouts**: Deploy to percentage of users first
- ✅ **Mandatory Updates**: Force critical updates
- ✅ **Automatic Rollback**: Revert on failures automatically
- ✅ **User Preferences**: Customizable update behavior
- ✅ **Device Monitoring**: Battery, network, and storage checks
- ✅ **Comprehensive Logging**: Detailed logs for debugging
- ✅ **Cross-Platform**: Works on iOS and Android

### Advanced Features
- ✅ **Patch Updates**: Download only changes (when available)
- ✅ **Background Updates**: Update when app is backgrounded
- ✅ **WiFi-Only Mode**: Restrict updates to WiFi connections
- ✅ **Battery Awareness**: Prevent updates on low battery
- ✅ **Update History**: Track all update attempts
- ✅ **Error Recovery**: Retry failed updates with exponential backoff
- ✅ **Settings UI**: User-friendly settings panel
- ✅ **Real-time Progress**: Detailed download/install progress

## Quick Start

### 1. Prerequisites

Ensure you have the following installed:
- React Native 0.60+ (for autolinking)
- Node.js 18+
- Stallion CLI: `npm install -g stallion-cli`

### 2. Installation

The required packages are already installed in this project:
- `react-native-stallion@^2.2.0`
- `stallion-cli@^2.3.1`

### 3. Configuration

1. **Create Stallion Account**: Sign up at [https://console.stalliontech.io/](https://console.stalliontech.io/)

2. **Configure Environment**: Copy `.env.example` to `.env` and fill in your credentials:
   ```env
   STALLION_APP_TOKEN=your-stallion-app-token
   STALLION_PROJECT_ID=your-stallion-project-id
   STALLION_ENVIRONMENT=development
   ```

3. **Update stallion.config.js**: The configuration is already set up to use environment variables.

### 4. First Deployment

```bash
# Build and deploy your first OTA update
npm run ota:build
npm run ota:deploy

# Or use the deployment script
node scripts/deploy-ota.js -e development -d "Initial OTA setup"
```

### 5. Test the Implementation

1. Build and install your app on a device
2. Deploy an update using the commands above
3. Launch the app - you should see the update modal

## Architecture

### Component Structure

```
src/
├── services/
│   └── stallionOTAService.ts      # Core OTA service
├── components/
│   ├── StallionUpdateModal.tsx    # Main update UI
│   └── StallionSettingsPanel.tsx  # Settings interface
└── types/
    └── stallion.ts                # TypeScript definitions
```

### Service Architecture

The OTA system follows a layered architecture:

1. **App Layer**: Main app integration and lifecycle management
2. **Service Layer**: Core OTA logic and state management
3. **UI Layer**: User interface components for updates
4. **SDK Layer**: React Native Stallion SDK integration
5. **Platform Layer**: Native iOS/Android implementations

### Data Flow

1. **Initialization**: Service initializes on app start
2. **Detection**: Periodic checks for updates
3. **Notification**: User notified via modal
4. **Download**: Update downloaded with progress tracking
5. **Installation**: Update applied automatically
6. **Verification**: Success verified, rollback if needed

## Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `STALLION_APP_TOKEN` | Your app token from Stallion dashboard | Yes | - |
| `STALLION_PROJECT_ID` | Your project ID from Stallion dashboard | Yes | - |
| `STALLION_ENVIRONMENT` | Target environment | No | `development` |
| `ENABLE_OTA_UPDATES` | Enable/disable OTA functionality | No | `true` |
| `DEBUG_LOGGING` | Enable debug logging | No | `true` |

### stallion.config.js Options

```javascript
module.exports = {
  // Credentials
  appToken: process.env.STALLION_APP_TOKEN,
  projectId: process.env.STALLION_PROJECT_ID,
  
  // Update Behavior
  checkOnStart: true,
  checkOnResume: true,
  autoUpdate: false,
  checkInterval: 30 * 60 * 1000, // 30 minutes
  
  // Security
  verifySignature: process.env.NODE_ENV === 'production',
  requireHttps: process.env.NODE_ENV === 'production',
  
  // Advanced
  enableAutoRollback: true,
  rollbackTimeout: 30000,
  maxRetries: 3,
  retryDelay: 5000,
};
```

## Usage

### Basic Usage

The OTA system works automatically once configured. However, you can also control it programmatically:

```typescript
import stallionOTAService from './src/services/stallionOTAService';

// Check for updates manually
const updateInfo = await stallionOTAService.checkForUpdates(true);

// Download and install update
if (updateInfo?.isAvailable) {
  await stallionOTAService.downloadAndInstall();
}

// Get service status
const status = stallionOTAService.getServiceStatus();
console.log('OTA Status:', status);
```

### User Preferences

```typescript
// Update user preferences
await stallionOTAService.updateUserPreferences({
  autoDownload: true,
  wifiOnly: true,
  minBatteryLevel: 30,
});

// Get current preferences
const preferences = stallionOTAService.getUserPreferences();
```

### Event Handling

```typescript
// Listen to update events
stallionOTAService.addEventListener((status, info, progress) => {
  switch (status) {
    case 'update_available':
      console.log('Update available:', info);
      break;
    case 'downloading':
      console.log('Download progress:', progress);
      break;
    case 'installing':
      console.log('Installing update...');
      break;
    case 'restart_required':
      console.log('Update installed, restart required');
      break;
  }
});
```

## Testing

### Automated Tests

```bash
# Run all tests
npm test

# Run OTA-specific tests
npm test -- --testPathPattern=stallion

# Run with coverage
npm test -- --coverage
```

### Manual Testing

See [TESTING_GUIDE.md](./TESTING_GUIDE.md) for comprehensive testing procedures.

### Test Environments

1. **Development**: `npm run ota:deploy`
2. **Staging**: `npm run ota:deploy:staging`
3. **Production**: `npm run ota:deploy:production`

## Deployment

### Using NPM Scripts

```bash
# Quick deployment to development
npm run ota:deploy

# Deploy to staging with description
npm run ota:deploy:staging -- -d "Bug fixes and improvements"

# Deploy to production (use with caution)
npm run ota:deploy:production
```

### Using Deployment Scripts

```bash
# Cross-platform Node.js script
node scripts/deploy-ota.js -e production -d "Critical update" -m -r 50

# Linux/macOS bash script
./scripts/deploy-ota.sh -e staging -d "New features" -r 75

# Windows batch script
scripts\deploy-ota.bat -e development -d "Testing update"
```

### Deployment Options

- `-e, --environment`: Target environment (development, staging, production)
- `-d, --description`: Update description
- `-m, --mandatory`: Mark as mandatory update
- `-r, --rollout`: Rollout percentage (1-100)
- `--dry-run`: Preview without deploying

## Troubleshooting

### Common Issues

#### 1. Updates Not Detected
- Check network connectivity
- Verify Stallion credentials in `.env`
- Ensure `ENABLE_OTA_UPDATES=true`
- Check logs for initialization errors

#### 2. Download Failures
- Verify sufficient storage space
- Check network stability
- Review firewall/proxy settings
- Check error logs in `logs/` directory

#### 3. Installation Failures
- Ensure app has necessary permissions
- Check bundle integrity
- Verify device compatibility
- Review native module linking

#### 4. Modal Not Showing
- Check if `showUpdateModal` state is managed correctly
- Verify event listeners are set up
- Check if update conditions are met (battery, network)

### Debug Mode

Enable debug logging in `.env`:
```env
DEBUG_LOGGING=true
```

### Log Files

Check logs in the `logs/` directory:
- Deployment logs: `logs/deploy-*.log`
- App logs: Check your logging service

### Getting Help

1. Check the [Stallion Documentation](https://learn.stalliontech.io/)
2. Review logs for detailed error messages
3. Test with `--dry-run` to preview actions
4. Use `npm run ota:status` to check deployment status

## API Reference

### stallionOTAService

#### Methods

- `initialize(): Promise<boolean>` - Initialize the service
- `checkForUpdates(force?: boolean): Promise<UpdateInfo | null>` - Check for updates
- `downloadUpdate(): Promise<boolean>` - Download available update
- `installUpdate(): Promise<boolean>` - Install downloaded update
- `downloadAndInstall(): Promise<boolean>` - Download and install in one step
- `restartApp(): Promise<void>` - Restart app to apply update
- `rollbackUpdate(): Promise<boolean>` - Rollback to previous version
- `getUserPreferences(): UpdatePreferences` - Get user preferences
- `updateUserPreferences(prefs: Partial<UpdatePreferences>): Promise<void>` - Update preferences
- `getUpdateHistory(): UpdateHistoryEntry[]` - Get update history
- `getDeviceStatus(): DeviceStatus` - Get device status
- `getServiceStatus(): ServiceStatus` - Get comprehensive service status
- `addEventListener(listener: UpdateEventListener): void` - Add event listener
- `removeEventListener(listener: UpdateEventListener): void` - Remove event listener
- `cleanup(): void` - Cleanup resources

#### Types

See the full type definitions in `src/services/stallionOTAService.ts`.

---

For more detailed information, see the individual documentation files in the `docs/` directory.
