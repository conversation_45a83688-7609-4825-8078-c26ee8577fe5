/**
 * Enhanced Stallion OTA Service
 *
 * Comprehensive service for handling Over-The-Air updates using React Native Stallion
 * Features: Update scheduling, rollback handling, battery/network checks, retry logic
 *
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import { AppState, AppStateStatus } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { Stallion } from 'react-native-stallion';
import DeviceInfo from 'react-native-device-info';
import AsyncStorage from '@react-native-async-storage/async-storage';
import logger from './productionLogger';

// Storage keys
const STORAGE_KEYS = {
  LAST_UPDATE_CHECK: 'stallion_last_update_check',
  UPDATE_RETRY_COUNT: 'stallion_update_retry_count',
  FAILED_UPDATES: 'stallion_failed_updates',
  UPDATE_HISTORY: 'stallion_update_history',
  USER_PREFERENCES: 'stallion_user_preferences',
};

// Update status types
export enum UpdateStatus {
  CHECKING = 'checking',
  DOWNLOADING = 'downloading',
  INSTALLING = 'installing',
  UP_TO_DATE = 'up_to_date',
  UPDATE_AVAILABLE = 'update_available',
  ERROR = 'error',
  RESTART_REQUIRED = 'restart_required',
}

// Enhanced update info interface
export interface UpdateInfo {
  isAvailable: boolean;
  isMandatory: boolean;
  version: string;
  buildNumber: number;
  description?: string;
  packageSize?: number;
  downloadUrl?: string;
  releaseDate?: string;
  changelog?: string[];
  minAppVersion?: string;
  targetPlatform?: 'android' | 'ios' | 'both';
}

// Update progress interface
export interface UpdateProgress {
  bytesReceived: number;
  totalBytes: number;
  percentage: number;
  speed?: number; // bytes per second
  estimatedTimeRemaining?: number; // seconds
}

// Update history entry
export interface UpdateHistoryEntry {
  version: string;
  buildNumber: number;
  timestamp: number;
  status: 'success' | 'failed' | 'rolled_back';
  error?: string;
}

// User preferences for updates
export interface UpdatePreferences {
  autoDownload: boolean;
  wifiOnly: boolean;
  minBatteryLevel: number;
  allowInBackground: boolean;
  notifyOnAvailable: boolean;
}

// Network info interface
interface NetworkInfo {
  isConnected: boolean;
  type: string;
  isWiFi: boolean;
  isMetered?: boolean;
}

// Event listeners type
type UpdateEventListener = (status: UpdateStatus, info?: UpdateInfo, progress?: UpdateProgress) => void;

class StallionOTAService {
  private isInitialized: boolean = false;
  private currentStatus: UpdateStatus = UpdateStatus.UP_TO_DATE;
  private currentUpdateInfo: UpdateInfo | null = null;
  private eventListeners: UpdateEventListener[] = [];
  private appStateSubscription: any = null;
  private checkInterval: NodeJS.Timeout | null = null;
  private retryCount: number = 0;
  private maxRetries: number = 3;
  private retryDelay: number = 5000;
  private updateHistory: UpdateHistoryEntry[] = [];
  private userPreferences: UpdatePreferences = {
    autoDownload: false,
    wifiOnly: false,
    minBatteryLevel: 20,
    allowInBackground: true,
    notifyOnAvailable: true,
  };
  private networkInfo: NetworkInfo = {
    isConnected: false,
    type: 'unknown',
    isWiFi: false,
  };
  private batteryLevel: number = 100;

  /**
   * Initialize the enhanced Stallion OTA service
   */
  async initialize(): Promise<boolean> {
    try {
      logger.info('[STALLION_OTA] Initializing enhanced Stallion OTA service...', { module: 'stallionOTA', action: 'initialize' });

      // Load user preferences and update history
      await this.loadStoredData();

      // Initialize device and network monitoring
      await this.initializeDeviceMonitoring();

      // Set up event listeners for Stallion events
      this.setupEventListeners();

      // Set up app state listener for background/foreground checks
      this.setupAppStateListener();

      // Set up network state listener
      this.setupNetworkListener();

      // Start periodic checks if enabled
      this.startPeriodicChecks();

      this.isInitialized = true;
      logger.info('[STALLION_OTA] Enhanced Stallion OTA service initialized successfully', {
        module: 'stallionOTA',
        action: 'initialize',
        preferences: this.userPreferences,
        historyCount: this.updateHistory.length
      });

      return true;
    } catch (error) {
      logger.error('[STALLION_OTA] Failed to initialize Stallion OTA service', error, 'stallionOTA');
      return false;
    }
  }

  /**
   * Load stored data from AsyncStorage
   */
  private async loadStoredData(): Promise<void> {
    try {
      // Load user preferences
      const prefsData = await AsyncStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
      if (prefsData) {
        this.userPreferences = { ...this.userPreferences, ...JSON.parse(prefsData) };
      }

      // Load update history
      const historyData = await AsyncStorage.getItem(STORAGE_KEYS.UPDATE_HISTORY);
      if (historyData) {
        this.updateHistory = JSON.parse(historyData);
      }

      // Load retry count
      const retryData = await AsyncStorage.getItem(STORAGE_KEYS.UPDATE_RETRY_COUNT);
      if (retryData) {
        this.retryCount = parseInt(retryData, 10) || 0;
      }
    } catch (error) {
      logger.warn('[STALLION_OTA] Failed to load stored data', { error }, 'stallionOTA');
    }
  }

  /**
   * Initialize device and network monitoring
   */
  private async initializeDeviceMonitoring(): Promise<void> {
    try {
      // Get initial battery level
      this.batteryLevel = await DeviceInfo.getBatteryLevel() * 100;

      // Get initial network state
      const netInfo = await NetInfo.fetch();
      this.networkInfo = {
        isConnected: netInfo.isConnected || false,
        type: netInfo.type || 'unknown',
        isWiFi: netInfo.type === 'wifi',
        isMetered: netInfo.details?.isConnectionExpensive,
      };

      logger.info('[STALLION_OTA] Device monitoring initialized', {
        battery: this.batteryLevel,
        network: this.networkInfo
      }, 'stallionOTA');
    } catch (error) {
      logger.warn('[STALLION_OTA] Failed to initialize device monitoring', { error }, 'stallionOTA');
    }
  }

  /**
   * Set up Stallion event listeners
   */
  private setupEventListeners(): void {
    // Update available
    Stallion.on('updateAvailable', (updateInfo: any) => {
      logger.info('[STALLION_OTA] Update available', { updateInfo }, 'stallionOTA');
      
      const info: UpdateInfo = {
        isAvailable: true,
        isMandatory: updateInfo.isMandatory || false,
        version: updateInfo.version,
        buildNumber: updateInfo.buildNumber,
        description: updateInfo.description,
        packageSize: updateInfo.packageSize,
        downloadUrl: updateInfo.downloadUrl,
      };

      this.currentUpdateInfo = info;
      this.updateStatus(UpdateStatus.UPDATE_AVAILABLE, info);
    });

    // Download progress
    Stallion.on('downloadProgress', (progress: any) => {
      const progressInfo: UpdateProgress = {
        bytesReceived: progress.bytesReceived,
        totalBytes: progress.totalBytes,
        percentage: Math.round((progress.bytesReceived / progress.totalBytes) * 100),
      };

      this.updateStatus(UpdateStatus.DOWNLOADING, this.currentUpdateInfo, progressInfo);
    });

    // Download complete
    Stallion.on('downloadComplete', () => {
      logger.info('[STALLION_OTA] Update download complete', null, 'stallionOTA');
      this.updateStatus(UpdateStatus.INSTALLING, this.currentUpdateInfo);
    });

    // Update installed
    Stallion.on('updateInstalled', () => {
      logger.info('[STALLION_OTA] Update installed successfully', null, 'stallionOTA');
      this.updateStatus(UpdateStatus.RESTART_REQUIRED, this.currentUpdateInfo);
    });

    // Update error
    Stallion.on('updateError', (error: any) => {
      logger.error('[STALLION_OTA] Update error occurred', error, 'stallionOTA');
      this.updateStatus(UpdateStatus.ERROR, this.currentUpdateInfo);
    });

    // No update available
    Stallion.on('upToDate', () => {
      logger.info('[STALLION_OTA] App is up to date', null, 'stallionOTA');
      this.updateStatus(UpdateStatus.UP_TO_DATE);
    });

    // Rollback occurred
    Stallion.on('rollback', (reason: string) => {
      logger.warn('[STALLION_OTA] Update rolled back', { reason }, 'stallionOTA');
      this.updateStatus(UpdateStatus.ERROR, null);
    });
  }

  /**
   * Set up network state listener
   */
  private setupNetworkListener(): void {
    NetInfo.addEventListener(state => {
      const wasConnected = this.networkInfo.isConnected;
      this.networkInfo = {
        isConnected: state.isConnected || false,
        type: state.type || 'unknown',
        isWiFi: state.type === 'wifi',
        isMetered: state.details?.isConnectionExpensive,
      };

      // If we just got connected and have a pending update, check conditions
      if (!wasConnected && this.networkInfo.isConnected && this.currentUpdateInfo) {
        this.evaluateUpdateConditions();
      }

      logger.debug('[STALLION_OTA] Network state changed', { networkInfo: this.networkInfo }, 'stallionOTA');
    });
  }

  /**
   * Set up app state listener for background/foreground update checks
   */
  private setupAppStateListener(): void {
    this.appStateSubscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active' && this.isInitialized) {
        // App came to foreground, check for updates if allowed
        if (this.userPreferences.allowInBackground || nextAppState === 'active') {
          this.checkForUpdates();
        }
      }
    });
  }

  /**
   * Evaluate if current conditions allow for updates
   */
  private async evaluateUpdateConditions(): Promise<boolean> {
    try {
      // Check network conditions
      if (!this.networkInfo.isConnected) {
        logger.debug('[STALLION_OTA] No network connection', null, 'stallionOTA');
        return false;
      }

      if (this.userPreferences.wifiOnly && !this.networkInfo.isWiFi) {
        logger.debug('[STALLION_OTA] WiFi required but not connected to WiFi', null, 'stallionOTA');
        return false;
      }

      // Check battery level
      const currentBattery = await DeviceInfo.getBatteryLevel() * 100;
      this.batteryLevel = currentBattery;

      if (currentBattery < this.userPreferences.minBatteryLevel) {
        logger.debug('[STALLION_OTA] Battery level too low for updates', {
          current: currentBattery,
          required: this.userPreferences.minBatteryLevel
        }, 'stallionOTA');
        return false;
      }

      return true;
    } catch (error) {
      logger.warn('[STALLION_OTA] Failed to evaluate update conditions', { error }, 'stallionOTA');
      return false;
    }
  }

  /**
   * Start periodic update checks
   */
  private startPeriodicChecks(): void {
    const config = require('../../stallion.config.js');
    
    if (config.checkInterval && config.checkInterval > 0) {
      this.checkInterval = setInterval(() => {
        this.checkForUpdates();
      }, config.checkInterval);
    }
  }

  /**
   * Check for available updates with enhanced logic
   */
  async checkForUpdates(force: boolean = false): Promise<UpdateInfo | null> {
    try {
      if (!this.isInitialized) {
        logger.warn('[STALLION_OTA] Service not initialized, skipping update check', null, 'stallionOTA');
        return null;
      }

      // Check if we should skip based on recent check (unless forced)
      if (!force && !await this.shouldCheckForUpdates()) {
        logger.debug('[STALLION_OTA] Skipping update check - too recent', null, 'stallionOTA');
        return null;
      }

      // Evaluate update conditions
      if (!await this.evaluateUpdateConditions()) {
        logger.debug('[STALLION_OTA] Update conditions not met', null, 'stallionOTA');
        return null;
      }

      logger.info('[STALLION_OTA] Checking for updates...', null, 'stallionOTA');
      this.updateStatus(UpdateStatus.CHECKING);

      const updateInfo = await this.checkForUpdatesWithRetry();

      if (updateInfo && updateInfo.available) {
        const info: UpdateInfo = {
          isAvailable: true,
          isMandatory: updateInfo.isMandatory || false,
          version: updateInfo.version,
          buildNumber: updateInfo.buildNumber,
          description: updateInfo.description,
          packageSize: updateInfo.packageSize,
          releaseDate: updateInfo.releaseDate,
          changelog: updateInfo.changelog,
          minAppVersion: updateInfo.minAppVersion,
          targetPlatform: updateInfo.targetPlatform,
        };

        this.currentUpdateInfo = info;
        this.updateStatus(UpdateStatus.UPDATE_AVAILABLE, info);

        // Store last check time
        await AsyncStorage.setItem(STORAGE_KEYS.LAST_UPDATE_CHECK, Date.now().toString());

        // Auto-download if enabled and conditions are met
        if (this.userPreferences.autoDownload && !info.isMandatory) {
          setTimeout(() => this.downloadUpdate(), 1000);
        }

        return info;
      } else {
        this.updateStatus(UpdateStatus.UP_TO_DATE);
        await AsyncStorage.setItem(STORAGE_KEYS.LAST_UPDATE_CHECK, Date.now().toString());
        return null;
      }
    } catch (error) {
      logger.error('[STALLION_OTA] Failed to check for updates', error, 'stallionOTA');
      await this.handleUpdateError(error);
      return null;
    }
  }

  /**
   * Check if we should perform an update check based on timing
   */
  private async shouldCheckForUpdates(): Promise<boolean> {
    try {
      const lastCheckStr = await AsyncStorage.getItem(STORAGE_KEYS.LAST_UPDATE_CHECK);
      if (!lastCheckStr) return true;

      const lastCheck = parseInt(lastCheckStr, 10);
      const now = Date.now();
      const timeSinceLastCheck = now - lastCheck;

      // Check at most once every 30 minutes
      const minInterval = 30 * 60 * 1000;
      return timeSinceLastCheck > minInterval;
    } catch (error) {
      logger.warn('[STALLION_OTA] Failed to check last update time', { error }, 'stallionOTA');
      return true;
    }
  }

  /**
   * Check for updates with retry logic
   */
  private async checkForUpdatesWithRetry(): Promise<any> {
    let lastError: any;

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        return await Stallion.checkForUpdate();
      } catch (error) {
        lastError = error;

        if (attempt < this.maxRetries) {
          logger.warn(`[STALLION_OTA] Update check failed, retrying (${attempt + 1}/${this.maxRetries})`, { error }, 'stallionOTA');
          await this.delay(this.retryDelay * (attempt + 1)); // Exponential backoff
        }
      }
    }

    throw lastError;
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Handle update errors with retry logic
   */
  private async handleUpdateError(error: any): Promise<void> {
    this.retryCount++;
    await AsyncStorage.setItem(STORAGE_KEYS.UPDATE_RETRY_COUNT, this.retryCount.toString());

    // Add to failed updates log
    const failedUpdates = await this.getFailedUpdates();
    failedUpdates.push({
      timestamp: Date.now(),
      error: error.message || String(error),
      retryCount: this.retryCount,
    });

    await AsyncStorage.setItem(STORAGE_KEYS.FAILED_UPDATES, JSON.stringify(failedUpdates.slice(-10))); // Keep last 10

    this.updateStatus(UpdateStatus.ERROR);
  }

  /**
   * Get failed updates from storage
   */
  private async getFailedUpdates(): Promise<any[]> {
    try {
      const data = await AsyncStorage.getItem(STORAGE_KEYS.FAILED_UPDATES);
      return data ? JSON.parse(data) : [];
    } catch {
      return [];
    }
  }

  /**
   * Add entry to update history
   */
  private async addToUpdateHistory(entry: UpdateHistoryEntry): Promise<void> {
    try {
      this.updateHistory.unshift(entry);
      this.updateHistory = this.updateHistory.slice(0, 50); // Keep last 50 entries
      await AsyncStorage.setItem(STORAGE_KEYS.UPDATE_HISTORY, JSON.stringify(this.updateHistory));
    } catch (error) {
      logger.warn('[STALLION_OTA] Failed to save update history', { error }, 'stallionOTA');
    }
  }

  /**
   * Download available update with enhanced error handling
   */
  async downloadUpdate(): Promise<boolean> {
    try {
      if (!this.currentUpdateInfo?.isAvailable) {
        logger.warn('[STALLION_OTA] No update available to download', null, 'stallionOTA');
        return false;
      }

      // Check conditions before downloading
      if (!await this.evaluateUpdateConditions()) {
        logger.warn('[STALLION_OTA] Update conditions not met for download', null, 'stallionOTA');
        return false;
      }

      logger.info('[STALLION_OTA] Starting update download...', {
        version: this.currentUpdateInfo.version,
        size: this.currentUpdateInfo.packageSize
      }, 'stallionOTA');

      this.updateStatus(UpdateStatus.DOWNLOADING, this.currentUpdateInfo);

      await Stallion.downloadUpdate();

      // Reset retry count on successful download
      this.retryCount = 0;
      await AsyncStorage.removeItem(STORAGE_KEYS.UPDATE_RETRY_COUNT);

      return true;
    } catch (error) {
      logger.error('[STALLION_OTA] Failed to download update', error, 'stallionOTA');
      await this.handleUpdateError(error);
      return false;
    }
  }

  /**
   * Install downloaded update with enhanced tracking
   */
  async installUpdate(): Promise<boolean> {
    try {
      if (!this.currentUpdateInfo) {
        logger.warn('[STALLION_OTA] No update info available for installation', null, 'stallionOTA');
        return false;
      }

      logger.info('[STALLION_OTA] Installing update...', {
        version: this.currentUpdateInfo.version,
        buildNumber: this.currentUpdateInfo.buildNumber
      }, 'stallionOTA');

      this.updateStatus(UpdateStatus.INSTALLING, this.currentUpdateInfo);

      await Stallion.applyUpdate();

      // Add successful installation to history
      await this.addToUpdateHistory({
        version: this.currentUpdateInfo.version,
        buildNumber: this.currentUpdateInfo.buildNumber,
        timestamp: Date.now(),
        status: 'success',
      });

      return true;
    } catch (error) {
      logger.error('[STALLION_OTA] Failed to install update', error, 'stallionOTA');

      // Add failed installation to history
      if (this.currentUpdateInfo) {
        await this.addToUpdateHistory({
          version: this.currentUpdateInfo.version,
          buildNumber: this.currentUpdateInfo.buildNumber,
          timestamp: Date.now(),
          status: 'failed',
          error: error.message || String(error),
        });
      }

      await this.handleUpdateError(error);
      return false;
    }
  }

  /**
   * Get user preferences for updates
   */
  getUserPreferences(): UpdatePreferences {
    return { ...this.userPreferences };
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(preferences: Partial<UpdatePreferences>): Promise<void> {
    try {
      this.userPreferences = { ...this.userPreferences, ...preferences };
      await AsyncStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(this.userPreferences));

      logger.info('[STALLION_OTA] User preferences updated', { preferences: this.userPreferences }, 'stallionOTA');
    } catch (error) {
      logger.error('[STALLION_OTA] Failed to update user preferences', error, 'stallionOTA');
    }
  }

  /**
   * Get update history
   */
  getUpdateHistory(): UpdateHistoryEntry[] {
    return [...this.updateHistory];
  }

  /**
   * Get current device and network status
   */
  getDeviceStatus(): { battery: number; network: NetworkInfo } {
    return {
      battery: this.batteryLevel,
      network: { ...this.networkInfo },
    };
  }

  /**
   * Force a rollback to previous version
   */
  async rollbackUpdate(): Promise<boolean> {
    try {
      logger.info('[STALLION_OTA] Initiating manual rollback...', null, 'stallionOTA');

      await Stallion.rollback();

      // Add rollback to history
      if (this.currentUpdateInfo) {
        await this.addToUpdateHistory({
          version: this.currentUpdateInfo.version,
          buildNumber: this.currentUpdateInfo.buildNumber,
          timestamp: Date.now(),
          status: 'rolled_back',
        });
      }

      return true;
    } catch (error) {
      logger.error('[STALLION_OTA] Failed to rollback update', error, 'stallionOTA');
      return false;
    }
  }

  /**
   * Download and install update in one step
   */
  async downloadAndInstall(): Promise<boolean> {
    const downloaded = await this.downloadUpdate();
    if (downloaded) {
      return await this.installUpdate();
    }
    return false;
  }

  /**
   * Restart app to apply update
   */
  async restartApp(): Promise<void> {
    try {
      logger.info('[STALLION_OTA] Restarting app to apply update...', null, 'stallionOTA');
      await Stallion.restartApp();
    } catch (error) {
      logger.error('[STALLION_OTA] Failed to restart app', error, 'stallionOTA');
    }
  }

  /**
   * Get current update status
   */
  getStatus(): UpdateStatus {
    return this.currentStatus;
  }

  /**
   * Get current update info
   */
  getUpdateInfo(): UpdateInfo | null {
    return this.currentUpdateInfo;
  }

  /**
   * Add event listener for update events
   */
  addEventListener(listener: UpdateEventListener): void {
    this.eventListeners.push(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(listener: UpdateEventListener): void {
    const index = this.eventListeners.indexOf(listener);
    if (index > -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  /**
   * Update status and notify listeners
   */
  private updateStatus(status: UpdateStatus, info?: UpdateInfo, progress?: UpdateProgress): void {
    this.currentStatus = status;
    
    // Notify all listeners
    this.eventListeners.forEach(listener => {
      try {
        listener(status, info, progress);
      } catch (error) {
        logger.error('[STALLION_OTA] Error in event listener', error, 'stallionOTA');
      }
    });
  }

  /**
   * Enhanced cleanup with proper resource management
   */
  cleanup(): void {
    logger.info('[STALLION_OTA] Cleaning up Stallion OTA service...', null, 'stallionOTA');

    // Remove app state listener
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }

    // Clear periodic check interval
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }

    // Clear all event listeners
    this.eventListeners = [];

    // Reset state
    this.isInitialized = false;
    this.currentStatus = UpdateStatus.UP_TO_DATE;
    this.currentUpdateInfo = null;
    this.retryCount = 0;

    logger.info('[STALLION_OTA] Stallion OTA service cleanup completed', null, 'stallionOTA');
  }

  /**
   * Get comprehensive service status for debugging
   */
  getServiceStatus(): {
    isInitialized: boolean;
    currentStatus: UpdateStatus;
    updateInfo: UpdateInfo | null;
    preferences: UpdatePreferences;
    deviceStatus: { battery: number; network: NetworkInfo };
    retryCount: number;
    historyCount: number;
  } {
    return {
      isInitialized: this.isInitialized,
      currentStatus: this.currentStatus,
      updateInfo: this.currentUpdateInfo,
      preferences: this.getUserPreferences(),
      deviceStatus: this.getDeviceStatus(),
      retryCount: this.retryCount,
      historyCount: this.updateHistory.length,
    };
  }
}

// Export singleton instance
export const stallionOTAService = new StallionOTAService();
export default stallionOTAService;
