# Stallion OTA Testing Guide

This guide provides comprehensive testing procedures for the React Native Stallion OTA implementation.

## Testing Environments

### 1. Development Environment
- **Purpose**: Initial testing and debugging
- **Configuration**: `STALLION_ENVIRONMENT=development`
- **Characteristics**: 
  - Immediate updates
  - Detailed logging
  - No rollout restrictions

### 2. Staging Environment
- **Purpose**: Pre-production testing
- **Configuration**: `STALLION_ENVIRONMENT=staging`
- **Characteristics**:
  - Production-like behavior
  - Limited audience
  - Rollout percentage testing

### 3. Production Environment
- **Purpose**: Live app updates
- **Configuration**: `STALLION_ENVIRONMENT=production`
- **Characteristics**:
  - Real users
  - Gradual rollouts
  - Monitoring and rollback capabilities

## Automated Testing

### Unit Tests

Run unit tests for the OTA service:

```bash
# Run all tests
npm test

# Run OTA service tests specifically
npm test -- --testPathPattern=stallionOTAService

# Run with coverage
npm test -- --coverage
```

### Integration Tests

Run integration tests for UI components:

```bash
# Run modal component tests
npm test -- --testPathPattern=StallionUpdateModal

# Run all component tests
npm test -- --testPathPattern=components
```

### Test Coverage Requirements

- **Service Layer**: Minimum 90% coverage
- **UI Components**: Minimum 85% coverage
- **Critical Paths**: 100% coverage (update flow, error handling)

## Manual Testing Procedures

### 1. Initial Setup Testing

#### Prerequisites Check
- [ ] Stallion CLI installed globally
- [ ] Environment variables configured
- [ ] stallion.config.js properly set up
- [ ] App builds successfully

#### Service Initialization
- [ ] App starts without errors
- [ ] OTA service initializes successfully
- [ ] Device status is correctly detected
- [ ] Network status is properly monitored

### 2. Update Detection Testing

#### Scenario: No Updates Available
1. Deploy app to test environment
2. Launch app
3. **Expected**: No update modal appears
4. **Verify**: Logs show "up to date" status

#### Scenario: Optional Update Available
1. Deploy new version to Stallion
2. Launch app or bring to foreground
3. **Expected**: Update modal appears with "Later" option
4. **Verify**: Update information is correctly displayed

#### Scenario: Mandatory Update Available
1. Deploy mandatory update to Stallion
2. Launch app
3. **Expected**: Update modal appears without "Later" option
4. **Verify**: Back button is disabled

### 3. Download and Installation Testing

#### Scenario: Successful Update
1. Trigger update download
2. **Expected**: Progress bar shows download progress
3. **Expected**: Installation begins automatically
4. **Expected**: App restarts with new version

#### Scenario: Network Interruption
1. Start update download
2. Disable network connection
3. **Expected**: Error message appears
4. **Expected**: Retry option available
5. Re-enable network and retry
6. **Expected**: Download resumes or restarts

#### Scenario: Low Battery
1. Set device battery below minimum threshold (or mock)
2. Attempt update
3. **Expected**: Warning message about low battery
4. **Expected**: Update blocked until battery sufficient

#### Scenario: WiFi-Only Preference
1. Enable WiFi-only setting
2. Switch to cellular connection
3. Attempt update
4. **Expected**: Warning about WiFi requirement
5. **Expected**: Update blocked until WiFi available

### 4. User Preference Testing

#### Settings Panel
- [ ] Settings panel opens correctly
- [ ] All preferences are displayed
- [ ] Preference changes are saved
- [ ] Device status is accurately shown
- [ ] Update history is displayed

#### Preference Behavior
- [ ] Auto-download works when enabled
- [ ] WiFi-only restriction is enforced
- [ ] Battery level requirement is respected
- [ ] Background updates work as configured

### 5. Error Handling Testing

#### Scenario: Download Failure
1. Simulate network error during download
2. **Expected**: Error message displayed
3. **Expected**: Retry button available
4. **Expected**: Rollback option shown

#### Scenario: Installation Failure
1. Simulate installation error
2. **Expected**: Error message displayed
3. **Expected**: App remains on previous version
4. **Expected**: Rollback option available

#### Scenario: Corrupted Update
1. Deploy corrupted update (if possible)
2. **Expected**: Verification fails
3. **Expected**: Update rejected
4. **Expected**: Error logged

### 6. Rollback Testing

#### Manual Rollback
1. Complete an update
2. Trigger manual rollback
3. **Expected**: Confirmation dialog appears
4. **Expected**: App reverts to previous version
5. **Expected**: Rollback logged in history

#### Automatic Rollback
1. Deploy update with intentional crash
2. **Expected**: App detects failure
3. **Expected**: Automatic rollback occurs
4. **Expected**: Previous version restored

### 7. Performance Testing

#### Update Size Impact
- [ ] Test with small updates (< 1MB)
- [ ] Test with medium updates (1-10MB)
- [ ] Test with large updates (> 10MB)
- [ ] Verify download times are reasonable
- [ ] Check memory usage during updates

#### Battery Impact
- [ ] Monitor battery usage during updates
- [ ] Verify updates don't drain battery excessively
- [ ] Test on low battery devices

#### Network Usage
- [ ] Monitor data usage during updates
- [ ] Test on slow network connections
- [ ] Verify updates work on metered connections

### 8. Multi-Device Testing

#### Device Types
- [ ] Test on Android phones
- [ ] Test on Android tablets
- [ ] Test on iOS phones
- [ ] Test on iOS tablets
- [ ] Test on different OS versions

#### Network Conditions
- [ ] WiFi connection
- [ ] 4G/LTE connection
- [ ] 3G connection
- [ ] Weak signal conditions
- [ ] Network switching scenarios

### 9. Edge Case Testing

#### App State Changes
- [ ] Update during app backgrounding
- [ ] Update during phone calls
- [ ] Update during low memory conditions
- [ ] Update during device rotation

#### Timing Issues
- [ ] Multiple rapid update checks
- [ ] Update available during app startup
- [ ] Update during critical user actions
- [ ] Concurrent update attempts

## Test Data and Scenarios

### Sample Update Descriptions
- "Bug fixes and performance improvements"
- "New features: Dark mode, improved search"
- "Critical security update - please install immediately"
- "UI improvements and accessibility enhancements"

### Test User Personas
1. **Power User**: Wants immediate updates, technical details
2. **Casual User**: Prefers automatic updates, minimal interruption
3. **Cautious User**: Wants to review updates before installing
4. **Enterprise User**: Needs controlled rollouts, compliance

## Monitoring and Metrics

### Key Metrics to Track
- Update adoption rate
- Download success rate
- Installation success rate
- Rollback frequency
- User preference distribution
- Error rates by type

### Logging Verification
- [ ] All update events are logged
- [ ] Error details are captured
- [ ] Performance metrics are recorded
- [ ] User actions are tracked

## Troubleshooting Common Issues

### Update Not Detected
1. Check network connectivity
2. Verify Stallion configuration
3. Check environment settings
4. Review server-side deployment

### Download Failures
1. Check available storage space
2. Verify network stability
3. Check firewall/proxy settings
4. Review error logs

### Installation Failures
1. Check app permissions
2. Verify bundle integrity
3. Check device compatibility
4. Review native module linking

## Sign-off Checklist

Before releasing OTA functionality:

- [ ] All automated tests pass
- [ ] Manual testing completed on all target devices
- [ ] Performance benchmarks met
- [ ] Error handling verified
- [ ] Rollback procedures tested
- [ ] Documentation updated
- [ ] Team training completed
- [ ] Monitoring systems configured

## Emergency Procedures

### Immediate Rollback
If a critical issue is discovered:

1. Execute immediate rollback:
   ```bash
   npm run ota:rollback
   ```

2. Verify rollback success
3. Investigate root cause
4. Prepare hotfix if needed
5. Communicate with stakeholders

### Incident Response
1. Assess impact and severity
2. Execute appropriate response plan
3. Document incident details
4. Conduct post-incident review
5. Update procedures as needed
