/**
 * React Native Stallion Configuration
 *
 * Configuration for OTA (Over-The-Air) updates using React Native Stallion
 * Dashboard: https://console.stalliontech.io/
 * Docs: https://learn.stalliontech.io/
 *
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

// Load environment variables
require('dotenv').config();

const packageJson = require('./package.json');

module.exports = {
  // ============================================================================
  // STALLION CREDENTIALS
  // ============================================================================

  /**
   * App Token from Stallion Dashboard
   * Get this from: https://console.stalliontech.io/
   * IMPORTANT: Set STALLION_APP_TOKEN in your .env file
   */
  appToken: process.env.STALLION_APP_TOKEN || 'your-stallion-app-token-here',

  /**
   * Project ID from Stallion Dashboard
   * Get this from: https://console.stalliontech.io/
   * IMPORTANT: Set STALLION_PROJECT_ID in your .env file
   */
  projectId: process.env.STALLION_PROJECT_ID || 'your-stallion-project-id-here',

  /**
   * Environment (development, staging, production)
   */
  environment: process.env.STALLION_ENVIRONMENT || 'development',

  // ============================================================================
  // APP INFORMATION
  // ============================================================================

  /**
   * App version - Automatically synced with package.json
   */
  appVersion: process.env.APP_VERSION || packageJson.version,

  /**
   * Build number - Incremented with each build
   */
  buildNumber: parseInt(process.env.BUILD_NUMBER || '1', 10),

  /**
   * App name - From package.json
   */
  appName: packageJson.name,

  // ============================================================================
  // UPDATE BEHAVIOR
  // ============================================================================

  /**
   * Automatically check for updates on app start
   */
  checkOnStart: process.env.ENABLE_OTA_UPDATES === 'true',

  /**
   * Check for updates when app comes to foreground
   */
  checkOnResume: process.env.ENABLE_OTA_UPDATES === 'true',

  /**
   * Automatically download and install updates (for non-mandatory updates)
   */
  autoUpdate: false, // Let user decide through UI

  /**
   * Interval for periodic update checks (in milliseconds)
   * Set to 0 to disable periodic checks
   */
  checkInterval: 30 * 60 * 1000, // 30 minutes

  /**
   * Timeout for update operations (in milliseconds)
   */
  timeout: 60000, // 60 seconds

  // ============================================================================
  // ROLLBACK CONFIGURATION
  // ============================================================================

  /**
   * Enable automatic rollback on update failure
   */
  enableAutoRollback: true,

  /**
   * Rollback timeout (in milliseconds)
   * If app doesn't start successfully within this time, rollback
   */
  rollbackTimeout: 30000, // 30 seconds

  // ============================================================================
  // SECURITY CONFIGURATION
  // ============================================================================

  /**
   * Verify update signatures (recommended for production)
   */
  verifySignature: process.env.NODE_ENV === 'production',

  /**
   * Allow updates only over HTTPS
   */
  requireHttps: process.env.NODE_ENV === 'production',

  // ============================================================================
  // DEVELOPMENT & DEBUGGING
  // ============================================================================

  /**
   * Enable debug logging
   */
  debug: process.env.DEBUG_LOGGING === 'true' || __DEV__,

  /**
   * Enable verbose logging for troubleshooting
   */
  verbose: process.env.NODE_ENV === 'development',

  /**
   * Log update events to console
   */
  logEvents: process.env.DEBUG_LOGGING === 'true' || __DEV__,

  // ============================================================================
  // ADVANCED CONFIGURATION
  // ============================================================================

  /**
   * Custom server URL (if using self-hosted Stallion)
   */
  serverUrl: process.env.STALLION_SERVER_URL || undefined,

  /**
   * Maximum download retries
   */
  maxRetries: 3,

  /**
   * Retry delay (in milliseconds)
   */
  retryDelay: 5000, // 5 seconds

  /**
   * Enable patch updates (when available)
   */
  enablePatchUpdates: true,

  /**
   * Minimum battery level required for updates (0-100)
   */
  minBatteryLevel: 20,

  /**
   * Only update on WiFi connection
   */
  wifiOnly: false,
};
