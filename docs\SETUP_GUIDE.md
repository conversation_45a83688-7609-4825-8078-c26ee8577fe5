# Stallion OTA Setup Guide

Step-by-step guide to set up React Native Stallion OTA updates in your Vendy app.

## Prerequisites

Before starting, ensure you have:

- ✅ React Native 0.60+ (for autolinking support)
- ✅ Node.js 18+ installed
- ✅ Android SDK (for Android builds)
- ✅ Xcode (for iOS builds, macOS only)
- ✅ A Stallion account (sign up at [console.stalliontech.io](https://console.stalliontech.io/))

## Step 1: Install Stallion CLI

Install the Stallion CLI globally:

```bash
npm install -g stallion-cli
```

Verify installation:
```bash
stallion --version
```

## Step 2: Create Stallion Project

1. **Sign up/Login** to [Stallion Console](https://console.stalliontech.io/)

2. **Create a new project**:
   - Click "New Project"
   - Enter project name: "Vendy"
   - Select platform: "React Native"
   - Choose your plan

3. **Get your credentials**:
   - Copy your **App Token**
   - Copy your **Project ID**
   - Note these down - you'll need them next

## Step 3: Configure Environment

1. **Copy environment template**:
   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` file** with your Stallion credentials:
   ```env
   # Stallion OTA Configuration
   STALLION_APP_TOKEN=your-stallion-app-token-here
   STALLION_PROJECT_ID=your-stallion-project-id-here
   STALLION_ENVIRONMENT=development
   
   # Enable OTA updates
   ENABLE_OTA_UPDATES=true
   DEBUG_LOGGING=true
   ```

3. **Verify stallion.config.js** (already configured):
   ```javascript
   module.exports = {
     appToken: process.env.STALLION_APP_TOKEN,
     projectId: process.env.STALLION_PROJECT_ID,
     environment: process.env.STALLION_ENVIRONMENT || 'development',
     // ... other settings
   };
   ```

## Step 4: Install Dependencies

The required packages are already installed, but verify they're present:

```bash
# Check if packages are installed
npm list react-native-stallion stallion-cli

# If missing, install them
npm install react-native-stallion@^2.2.0
npm install --save-dev stallion-cli@^2.3.1
```

## Step 5: Native Platform Setup

### Android Setup

1. **Verify permissions** in `android/app/src/main/AndroidManifest.xml`:
   ```xml
   <uses-permission android:name="android.permission.INTERNET" />
   <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
   ```

2. **Check ProGuard rules** (if using ProGuard) in `android/app/proguard-rules.pro`:
   ```proguard
   # React Native Stallion
   -keep class com.stallion.** { *; }
   -keep class io.stallion.** { *; }
   -dontwarn com.stallion.**
   -dontwarn io.stallion.**
   ```

### iOS Setup

1. **Install CocoaPods dependencies**:
   ```bash
   cd ios && pod install && cd ..
   ```

2. **Verify Info.plist** has network security settings in `ios/payvendy/Info.plist`:
   ```xml
   <key>NSAppTransportSecurity</key>
   <dict>
       <key>NSAllowsArbitraryLoads</key>
       <false/>
       <key>NSAllowsLocalNetworking</key>
       <true/>
   </dict>
   ```

## Step 6: Build and Test

1. **Clean build** (recommended):
   ```bash
   # Clean Metro cache
   npx react-native start --reset-cache
   
   # Clean Android build
   cd android && ./gradlew clean && cd ..
   
   # Clean iOS build (macOS only)
   cd ios && xcodebuild clean && cd ..
   ```

2. **Build and run**:
   ```bash
   # Android
   npx react-native run-android
   
   # iOS (macOS only)
   npx react-native run-ios
   ```

3. **Verify OTA initialization**:
   - Check logs for: "Stallion OTA service initialized successfully"
   - No error messages related to Stallion
   - App starts without crashes

## Step 7: Deploy Your First Update

1. **Build the OTA bundle**:
   ```bash
   npm run ota:build
   ```

2. **Deploy to development**:
   ```bash
   npm run ota:deploy
   ```

   Or with description:
   ```bash
   node scripts/deploy-ota.js -e development -d "Initial OTA setup test"
   ```

3. **Check deployment status**:
   ```bash
   npm run ota:status
   ```

## Step 8: Test the Update Flow

1. **Launch your app** on a device/emulator

2. **Trigger update check**:
   - The app should automatically check for updates on startup
   - Or bring the app to foreground to trigger a check

3. **Verify update modal appears**:
   - Should show "Update Available" modal
   - Display version information
   - Show download/install options

4. **Test update installation**:
   - Click "Download Update"
   - Watch progress bar
   - App should restart with new version

## Step 9: Configure for Different Environments

### Development Environment
```env
STALLION_ENVIRONMENT=development
DEBUG_LOGGING=true
ENABLE_OTA_UPDATES=true
```

### Staging Environment
```env
STALLION_ENVIRONMENT=staging
DEBUG_LOGGING=true
ENABLE_OTA_UPDATES=true
```

### Production Environment
```env
STALLION_ENVIRONMENT=production
DEBUG_LOGGING=false
ENABLE_OTA_UPDATES=true
```

## Step 10: Set Up Deployment Scripts

The deployment scripts are already configured. Test them:

```bash
# Test Node.js script
node scripts/deploy-ota.js --help

# Test bash script (Linux/macOS)
chmod +x scripts/deploy-ota.sh
./scripts/deploy-ota.sh --help

# Test batch script (Windows)
scripts\deploy-ota.bat --help
```

## Verification Checklist

Before going live, verify:

- [ ] ✅ Stallion CLI installed and working
- [ ] ✅ Environment variables configured
- [ ] ✅ App builds successfully on both platforms
- [ ] ✅ OTA service initializes without errors
- [ ] ✅ Update modal appears when update is available
- [ ] ✅ Download and installation work correctly
- [ ] ✅ App restarts with new version
- [ ] ✅ Rollback works if needed
- [ ] ✅ Deployment scripts work
- [ ] ✅ All environments configured (dev, staging, prod)

## Troubleshooting Setup Issues

### Issue: "stallion-cli not found"
**Solution**: Install globally: `npm install -g stallion-cli`

### Issue: "No Firebase App" errors
**Solution**: This is unrelated to Stallion. The OTA system works independently of Firebase.

### Issue: "Module not found: react-native-stallion"
**Solution**: 
```bash
npm install react-native-stallion
# For React Native < 0.60
npx react-native link react-native-stallion
```

### Issue: Build errors on Android
**Solution**:
```bash
cd android && ./gradlew clean && cd ..
npx react-native run-android
```

### Issue: Build errors on iOS
**Solution**:
```bash
cd ios && pod install && cd ..
npx react-native run-ios
```

### Issue: "Invalid credentials" error
**Solution**: 
- Verify `STALLION_APP_TOKEN` and `STALLION_PROJECT_ID` in `.env`
- Check credentials in Stallion Console
- Ensure no extra spaces or quotes

### Issue: Updates not detected
**Solution**:
- Check network connectivity
- Verify `ENABLE_OTA_UPDATES=true`
- Check app logs for initialization errors
- Try force refresh: bring app to foreground

## Next Steps

After successful setup:

1. **Read the full documentation**: [OTA_IMPLEMENTATION.md](./OTA_IMPLEMENTATION.md)
2. **Review testing procedures**: [TESTING_GUIDE.md](./TESTING_GUIDE.md)
3. **Set up monitoring**: Configure logging and analytics
4. **Plan your rollout strategy**: Start with small percentages
5. **Train your team**: Ensure everyone knows the deployment process

## Getting Help

If you encounter issues:

1. **Check logs**: Look in `logs/` directory for deployment logs
2. **Enable debug mode**: Set `DEBUG_LOGGING=true` in `.env`
3. **Test with dry run**: Use `--dry-run` flag to preview actions
4. **Consult documentation**: [Stallion Docs](https://learn.stalliontech.io/)
5. **Check native integration**: Review [NATIVE_INTEGRATION.md](./NATIVE_INTEGRATION.md)

## Security Considerations

For production deployments:

- ✅ Use environment-specific tokens
- ✅ Enable signature verification
- ✅ Use HTTPS-only mode
- ✅ Implement proper access controls
- ✅ Monitor deployment logs
- ✅ Set up rollback procedures
- ✅ Test thoroughly in staging first

---

**Congratulations!** 🎉 You now have a fully functional OTA update system. Your users will receive seamless updates without app store delays.
