# Vendy App Environment Configuration
# Copy this file to .env and fill in your actual values

# ============================================================================
# STALLION OTA CONFIGURATION
# ============================================================================

# Stallion App Token - Get this from https://console.stalliontech.io/
# This token identifies your app in the Stallion dashboard
STALLION_APP_TOKEN=your-stallion-app-token-here

# Stallion Project ID - Get this from https://console.stalliontech.io/
# This identifies your project in the Stallion dashboard
STALLION_PROJECT_ID=your-stallion-project-id-here

# Stallion Environment (development, staging, production)
STALLION_ENVIRONMENT=development

# ============================================================================
# APP CONFIGURATION
# ============================================================================

# App Environment
NODE_ENV=development

# App Version (should match package.json version)
APP_VERSION=1.0.0

# Build Number (increment for each build)
BUILD_NUMBER=1

# ============================================================================
# FIREBASE CONFIGURATION
# ============================================================================

# Firebase configuration is handled through google-services.json and GoogleService-Info.plist
# No environment variables needed for Firebase

# ============================================================================
# API CONFIGURATION
# ============================================================================

# Backend API URL
API_BASE_URL=http://localhost:3000/api

# API Timeout (in milliseconds)
API_TIMEOUT=30000

# ============================================================================
# DEVELOPMENT CONFIGURATION
# ============================================================================

# Enable debug logging
DEBUG_LOGGING=true

# Enable performance monitoring
PERFORMANCE_MONITORING=true

# Enable crash reporting
CRASH_REPORTING=true

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================

# Encryption key for local storage (use a strong key in production)
STORAGE_ENCRYPTION_KEY=vendy-encryption-key-2024-change-in-production

# ============================================================================
# FEATURE FLAGS
# ============================================================================

# Enable OTA updates
ENABLE_OTA_UPDATES=true

# Enable biometric authentication
ENABLE_BIOMETRIC_AUTH=true

# Enable push notifications
ENABLE_PUSH_NOTIFICATIONS=true
