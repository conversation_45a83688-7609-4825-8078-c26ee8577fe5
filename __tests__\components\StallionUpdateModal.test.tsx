/**
 * Integration Tests for Stallion Update Modal
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { Alert } from 'react-native';
import StallionUpdateModal from '../../src/components/StallionUpdateModal';
import stallionOTAService, { UpdateStatus, UpdateInfo } from '../../src/services/stallionOTAService';

// Mock dependencies
jest.mock('../../src/services/stallionOTAService');
jest.mock('../../src/services/productionLogger');
jest.mock('react-native-linear-gradient', () => 'LinearGradient');

const mockStallionOTAService = stallionOTAService as jest.Mocked<typeof stallionOTAService>;

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('StallionUpdateModal', () => {
  const mockOnClose = jest.fn();
  
  const mockUpdateInfo: UpdateInfo = {
    isAvailable: true,
    isMandatory: false,
    version: '1.1.0',
    buildNumber: 2,
    description: 'Bug fixes and improvements',
    packageSize: 1024000,
    changelog: ['Fixed login issue', 'Improved performance', 'Updated UI'],
  };

  const mockUserPreferences = {
    autoDownload: false,
    wifiOnly: false,
    minBatteryLevel: 20,
    allowInBackground: true,
    notifyOnAvailable: true,
  };

  const mockDeviceStatus = {
    battery: 80,
    network: {
      isConnected: true,
      type: 'wifi',
      isWiFi: true,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    mockStallionOTAService.getUserPreferences.mockReturnValue(mockUserPreferences);
    mockStallionOTAService.getUpdateHistory.mockReturnValue([]);
    mockStallionOTAService.getDeviceStatus.mockReturnValue(mockDeviceStatus);
    mockStallionOTAService.addEventListener.mockImplementation(() => {});
    mockStallionOTAService.removeEventListener.mockImplementation(() => {});
    mockStallionOTAService.downloadAndInstall.mockResolvedValue(true);
    mockStallionOTAService.updateUserPreferences.mockResolvedValue();
  });

  describe('Rendering', () => {
    it('should not render when not visible', () => {
      const { queryByText } = render(
        <StallionUpdateModal visible={false} onClose={mockOnClose} />
      );
      
      expect(queryByText('Update Available')).toBeNull();
    });

    it('should render update modal when visible', async () => {
      const { getByText } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      await waitFor(() => {
        expect(getByText('Update Available')).toBeTruthy();
      });
    });

    it('should render mandatory update modal', async () => {
      const { getByText } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      // Simulate mandatory update
      act(() => {
        const eventListener = mockStallionOTAService.addEventListener.mock.calls[0][0];
        eventListener(UpdateStatus.UPDATE_AVAILABLE, { ...mockUpdateInfo, isMandatory: true });
      });
      
      await waitFor(() => {
        expect(getByText('Mandatory Update')).toBeTruthy();
      });
    });

    it('should display update information correctly', async () => {
      const { getByText } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      // Simulate update available
      act(() => {
        const eventListener = mockStallionOTAService.addEventListener.mock.calls[0][0];
        eventListener(UpdateStatus.UPDATE_AVAILABLE, mockUpdateInfo);
      });
      
      await waitFor(() => {
        expect(getByText('Version 1.1.0 (Build 2)')).toBeTruthy();
        expect(getByText('Bug fixes and improvements')).toBeTruthy();
        expect(getByText('Size: 1.0 MB')).toBeTruthy();
      });
    });

    it('should display changelog when available', async () => {
      const { getByText } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      act(() => {
        const eventListener = mockStallionOTAService.addEventListener.mock.calls[0][0];
        eventListener(UpdateStatus.UPDATE_AVAILABLE, mockUpdateInfo);
      });
      
      await waitFor(() => {
        expect(getByText("What's New:")).toBeTruthy();
        expect(getByText('• Fixed login issue')).toBeTruthy();
        expect(getByText('• Improved performance')).toBeTruthy();
      });
    });
  });

  describe('User Interactions', () => {
    it('should handle download update button press', async () => {
      const { getByText } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      act(() => {
        const eventListener = mockStallionOTAService.addEventListener.mock.calls[0][0];
        eventListener(UpdateStatus.UPDATE_AVAILABLE, mockUpdateInfo);
      });
      
      await waitFor(() => {
        const downloadButton = getByText('Download Update');
        fireEvent.press(downloadButton);
      });
      
      expect(mockStallionOTAService.downloadAndInstall).toHaveBeenCalled();
    });

    it('should handle skip update for optional updates', async () => {
      const { getByText } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      act(() => {
        const eventListener = mockStallionOTAService.addEventListener.mock.calls[0][0];
        eventListener(UpdateStatus.UPDATE_AVAILABLE, mockUpdateInfo);
      });
      
      await waitFor(() => {
        const laterButton = getByText('Later');
        fireEvent.press(laterButton);
      });
      
      expect(mockOnClose).toHaveBeenCalled();
    });

    it('should prevent skipping mandatory updates', async () => {
      const { getByText, queryByText } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      act(() => {
        const eventListener = mockStallionOTAService.addEventListener.mock.calls[0][0];
        eventListener(UpdateStatus.UPDATE_AVAILABLE, { ...mockUpdateInfo, isMandatory: true });
      });
      
      await waitFor(() => {
        expect(getByText('Update Now')).toBeTruthy();
        expect(queryByText('Later')).toBeNull();
      });
    });

    it('should show alert for low battery', async () => {
      // Mock low battery
      mockStallionOTAService.getDeviceStatus.mockReturnValue({
        ...mockDeviceStatus,
        battery: 15, // Below minimum
      });
      
      const { getByText } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      act(() => {
        const eventListener = mockStallionOTAService.addEventListener.mock.calls[0][0];
        eventListener(UpdateStatus.UPDATE_AVAILABLE, mockUpdateInfo);
      });
      
      await waitFor(() => {
        const downloadButton = getByText('Download Update');
        fireEvent.press(downloadButton);
      });
      
      expect(Alert.alert).toHaveBeenCalledWith(
        'Low Battery',
        expect.stringContaining('Battery level is 15%')
      );
    });

    it('should show alert for WiFi requirement', async () => {
      // Mock cellular connection with WiFi-only preference
      mockStallionOTAService.getUserPreferences.mockReturnValue({
        ...mockUserPreferences,
        wifiOnly: true,
      });
      
      mockStallionOTAService.getDeviceStatus.mockReturnValue({
        ...mockDeviceStatus,
        network: {
          isConnected: true,
          type: 'cellular',
          isWiFi: false,
        },
      });
      
      const { getByText } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      act(() => {
        const eventListener = mockStallionOTAService.addEventListener.mock.calls[0][0];
        eventListener(UpdateStatus.UPDATE_AVAILABLE, mockUpdateInfo);
      });
      
      await waitFor(() => {
        const downloadButton = getByText('Download Update');
        fireEvent.press(downloadButton);
      });
      
      expect(Alert.alert).toHaveBeenCalledWith(
        'WiFi Required',
        expect.stringContaining('Your settings require WiFi')
      );
    });
  });

  describe('Progress Display', () => {
    it('should show download progress', async () => {
      const { getByText } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      // Simulate downloading status with progress
      act(() => {
        const eventListener = mockStallionOTAService.addEventListener.mock.calls[0][0];
        eventListener(
          UpdateStatus.DOWNLOADING,
          mockUpdateInfo,
          {
            bytesReceived: 512000,
            totalBytes: 1024000,
            percentage: 50,
            speed: 102400, // 100 KB/s
            estimatedTimeRemaining: 5,
          }
        );
      });
      
      await waitFor(() => {
        expect(getByText('Downloading')).toBeTruthy();
        expect(getByText('50%')).toBeTruthy();
        expect(getByText('512.0 KB / 1.0 MB')).toBeTruthy();
        expect(getByText('100.0 KB/s')).toBeTruthy();
      });
    });

    it('should show installation progress', async () => {
      const { getByText } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      act(() => {
        const eventListener = mockStallionOTAService.addEventListener.mock.calls[0][0];
        eventListener(UpdateStatus.INSTALLING, mockUpdateInfo);
      });
      
      await waitFor(() => {
        expect(getByText('Installing')).toBeTruthy();
        expect(getByText('100%')).toBeTruthy();
      });
    });
  });

  describe('Error Handling', () => {
    it('should display error state', async () => {
      const { getByText } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      act(() => {
        const eventListener = mockStallionOTAService.addEventListener.mock.calls[0][0];
        eventListener(UpdateStatus.ERROR, mockUpdateInfo);
      });
      
      await waitFor(() => {
        expect(getByText('Update failed. Please check your internet connection and try again.')).toBeTruthy();
        expect(getByText('Try Again')).toBeTruthy();
        expect(getByText('Rollback to Previous Version')).toBeTruthy();
      });
    });

    it('should handle rollback button press', async () => {
      mockStallionOTAService.rollbackUpdate.mockResolvedValue(true);
      
      const { getByText } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      act(() => {
        const eventListener = mockStallionOTAService.addEventListener.mock.calls[0][0];
        eventListener(UpdateStatus.ERROR, mockUpdateInfo);
      });
      
      await waitFor(() => {
        const rollbackButton = getByText('Rollback to Previous Version');
        fireEvent.press(rollbackButton);
      });
      
      // Should show confirmation alert
      expect(Alert.alert).toHaveBeenCalledWith(
        'Rollback Update',
        expect.stringContaining('Are you sure you want to rollback'),
        expect.arrayContaining([
          expect.objectContaining({ text: 'Cancel' }),
          expect.objectContaining({ text: 'Rollback' }),
        ])
      );
    });
  });

  describe('Device Status Display', () => {
    it('should display device status correctly', async () => {
      const { getByText } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      await waitFor(() => {
        expect(getByText('80%')).toBeTruthy(); // Battery
        expect(getByText('WiFi')).toBeTruthy(); // Network type
      });
    });

    it('should update device status during updates', async () => {
      const { rerender } = render(
        <StallionUpdateModal visible={true} onClose={mockOnClose} />
      );
      
      // Change device status
      mockStallionOTAService.getDeviceStatus.mockReturnValue({
        battery: 75,
        network: {
          isConnected: true,
          type: 'cellular',
          isWiFi: false,
        },
      });
      
      act(() => {
        const eventListener = mockStallionOTAService.addEventListener.mock.calls[0][0];
        eventListener(UpdateStatus.DOWNLOADING, mockUpdateInfo);
      });
      
      rerender(<StallionUpdateModal visible={true} onClose={mockOnClose} />);
      
      await waitFor(() => {
        expect(getByText('75%')).toBeTruthy();
        expect(getByText('cellular')).toBeTruthy();
      });
    });
  });
});
