/**
 * Unit Tests for Stallion OTA Service
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import DeviceInfo from 'react-native-device-info';
import { Stallion } from 'react-native-stallion';
import stallionOTAService, { UpdateStatus, UpdateInfo } from '../../src/services/stallionOTAService';

// Mock React Native modules
jest.mock('react-native', () => ({
  AppState: {
    addEventListener: jest.fn(() => ({ remove: jest.fn() })),
  },
}));

// Mock dependencies
jest.mock('@react-native-async-storage/async-storage');
jest.mock('@react-native-community/netinfo');
jest.mock('react-native-device-info');
jest.mock('react-native-stallion');
jest.mock('../../src/services/productionLogger');

const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;
const mockNetInfo = NetInfo as jest.Mocked<typeof NetInfo>;
const mockDeviceInfo = DeviceInfo as jest.Mocked<typeof DeviceInfo>;
const mockStallion = Stallion as jest.Mocked<typeof Stallion>;

describe('StallionOTAService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    mockAsyncStorage.getItem.mockResolvedValue(null);
    mockAsyncStorage.setItem.mockResolvedValue();
    mockAsyncStorage.removeItem.mockResolvedValue();
    
    mockDeviceInfo.getBatteryLevel.mockResolvedValue(0.8); // 80%
    
    mockNetInfo.fetch.mockResolvedValue({
      isConnected: true,
      type: 'wifi',
      details: { isConnectionExpensive: false }
    } as any);
    
    mockNetInfo.addEventListener.mockReturnValue(() => {});
    
    mockStallion.on.mockImplementation(() => {});
    mockStallion.checkForUpdate.mockResolvedValue({ available: false });
  });

  afterEach(() => {
    stallionOTAService.cleanup();
  });

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      const result = await stallionOTAService.initialize();
      
      expect(result).toBe(true);
      expect(mockDeviceInfo.getBatteryLevel).toHaveBeenCalled();
      expect(mockNetInfo.fetch).toHaveBeenCalled();
      expect(mockStallion.on).toHaveBeenCalledWith('updateAvailable', expect.any(Function));
    });

    it('should load stored preferences on initialization', async () => {
      const mockPreferences = {
        autoDownload: true,
        wifiOnly: true,
        minBatteryLevel: 30,
        allowInBackground: false,
        notifyOnAvailable: true,
      };
      
      mockAsyncStorage.getItem.mockImplementation((key) => {
        if (key === 'stallion_user_preferences') {
          return Promise.resolve(JSON.stringify(mockPreferences));
        }
        return Promise.resolve(null);
      });

      await stallionOTAService.initialize();
      const preferences = stallionOTAService.getUserPreferences();
      
      expect(preferences).toMatchObject(mockPreferences);
    });

    it('should handle initialization errors gracefully', async () => {
      mockDeviceInfo.getBatteryLevel.mockRejectedValue(new Error('Battery info unavailable'));
      
      const result = await stallionOTAService.initialize();
      
      expect(result).toBe(true); // Should still initialize despite device info errors
    });
  });

  describe('Update Checking', () => {
    beforeEach(async () => {
      await stallionOTAService.initialize();
    });

    it('should check for updates successfully', async () => {
      const mockUpdateInfo = {
        available: true,
        version: '1.1.0',
        buildNumber: 2,
        isMandatory: false,
        description: 'Bug fixes',
        packageSize: 1024000,
      };
      
      mockStallion.checkForUpdate.mockResolvedValue(mockUpdateInfo);
      
      const result = await stallionOTAService.checkForUpdates(true); // Force check
      
      expect(result).toMatchObject({
        isAvailable: true,
        version: '1.1.0',
        buildNumber: 2,
        isMandatory: false,
      });
      expect(mockStallion.checkForUpdate).toHaveBeenCalled();
    });

    it('should return null when no update is available', async () => {
      mockStallion.checkForUpdate.mockResolvedValue({ available: false });
      
      const result = await stallionOTAService.checkForUpdates(true);
      
      expect(result).toBeNull();
    });

    it('should respect timing constraints for update checks', async () => {
      // Set recent check time
      const recentTime = Date.now() - 10 * 60 * 1000; // 10 minutes ago
      mockAsyncStorage.getItem.mockImplementation((key) => {
        if (key === 'stallion_last_update_check') {
          return Promise.resolve(recentTime.toString());
        }
        return Promise.resolve(null);
      });

      const result = await stallionOTAService.checkForUpdates(); // Not forced
      
      expect(result).toBeNull();
      expect(mockStallion.checkForUpdate).not.toHaveBeenCalled();
    });

    it('should handle network conditions', async () => {
      // Mock no network connection
      mockNetInfo.fetch.mockResolvedValue({
        isConnected: false,
        type: 'none',
      } as any);
      
      await stallionOTAService.initialize(); // Re-initialize with no network
      
      const result = await stallionOTAService.checkForUpdates(true);
      
      expect(result).toBeNull();
    });

    it('should handle low battery conditions', async () => {
      mockDeviceInfo.getBatteryLevel.mockResolvedValue(0.1); // 10% battery
      
      await stallionOTAService.initialize(); // Re-initialize with low battery
      
      const result = await stallionOTAService.checkForUpdates(true);
      
      expect(result).toBeNull();
    });
  });

  describe('Update Download', () => {
    beforeEach(async () => {
      await stallionOTAService.initialize();
      
      // Set up a mock update
      const mockUpdateInfo = {
        available: true,
        version: '1.1.0',
        buildNumber: 2,
        isMandatory: false,
      };
      mockStallion.checkForUpdate.mockResolvedValue(mockUpdateInfo);
      await stallionOTAService.checkForUpdates(true);
    });

    it('should download update successfully', async () => {
      mockStallion.downloadUpdate.mockResolvedValue(undefined);
      
      const result = await stallionOTAService.downloadUpdate();
      
      expect(result).toBe(true);
      expect(mockStallion.downloadUpdate).toHaveBeenCalled();
    });

    it('should handle download failures', async () => {
      mockStallion.downloadUpdate.mockRejectedValue(new Error('Download failed'));
      
      const result = await stallionOTAService.downloadUpdate();
      
      expect(result).toBe(false);
    });

    it('should not download when no update is available', async () => {
      stallionOTAService.cleanup();
      await stallionOTAService.initialize(); // Reset state
      
      const result = await stallionOTAService.downloadUpdate();
      
      expect(result).toBe(false);
      expect(mockStallion.downloadUpdate).not.toHaveBeenCalled();
    });
  });

  describe('User Preferences', () => {
    beforeEach(async () => {
      await stallionOTAService.initialize();
    });

    it('should update user preferences', async () => {
      const newPreferences = {
        autoDownload: true,
        wifiOnly: true,
      };
      
      await stallionOTAService.updateUserPreferences(newPreferences);
      
      const preferences = stallionOTAService.getUserPreferences();
      expect(preferences.autoDownload).toBe(true);
      expect(preferences.wifiOnly).toBe(true);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'stallion_user_preferences',
        expect.stringContaining('"autoDownload":true')
      );
    });

    it('should return current preferences', () => {
      const preferences = stallionOTAService.getUserPreferences();
      
      expect(preferences).toHaveProperty('autoDownload');
      expect(preferences).toHaveProperty('wifiOnly');
      expect(preferences).toHaveProperty('minBatteryLevel');
      expect(preferences).toHaveProperty('allowInBackground');
      expect(preferences).toHaveProperty('notifyOnAvailable');
    });
  });

  describe('Service Status', () => {
    it('should return comprehensive service status', async () => {
      await stallionOTAService.initialize();
      
      const status = stallionOTAService.getServiceStatus();
      
      expect(status).toHaveProperty('isInitialized', true);
      expect(status).toHaveProperty('currentStatus');
      expect(status).toHaveProperty('preferences');
      expect(status).toHaveProperty('deviceStatus');
      expect(status).toHaveProperty('retryCount');
      expect(status).toHaveProperty('historyCount');
    });

    it('should return device status', async () => {
      await stallionOTAService.initialize();
      
      const deviceStatus = stallionOTAService.getDeviceStatus();
      
      expect(deviceStatus).toHaveProperty('battery');
      expect(deviceStatus).toHaveProperty('network');
      expect(deviceStatus.network).toHaveProperty('isConnected');
      expect(deviceStatus.network).toHaveProperty('type');
      expect(deviceStatus.network).toHaveProperty('isWiFi');
    });
  });

  describe('Cleanup', () => {
    it('should cleanup resources properly', async () => {
      await stallionOTAService.initialize();
      
      stallionOTAService.cleanup();
      
      const status = stallionOTAService.getServiceStatus();
      expect(status.isInitialized).toBe(false);
    });
  });

  describe('Event Handling', () => {
    it('should handle event listeners', async () => {
      await stallionOTAService.initialize();
      
      const mockListener = jest.fn();
      stallionOTAService.addEventListener(mockListener);
      
      // Simulate an update available event
      const updateInfo: UpdateInfo = {
        isAvailable: true,
        isMandatory: false,
        version: '1.1.0',
        buildNumber: 2,
      };
      
      // Trigger the event manually (in real scenario, this would come from Stallion)
      stallionOTAService['updateStatus'](UpdateStatus.UPDATE_AVAILABLE, updateInfo);
      
      expect(mockListener).toHaveBeenCalledWith(UpdateStatus.UPDATE_AVAILABLE, updateInfo, undefined);
      
      stallionOTAService.removeEventListener(mockListener);
    });
  });
});
